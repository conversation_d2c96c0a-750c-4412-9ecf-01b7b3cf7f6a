// Background script for the Trad Console Monitor extension

console.log('Trad Console Monitor background script loaded');

// Store console logs for each tab
const tabLogs = new Map<number, any[]>();

// Listen for messages from content scripts
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'CONSOLE_LOG' && sender.tab?.id) {
    const tabId = sender.tab.id;
    
    // Store the log for this tab
    if (!tabLogs.has(tabId)) {
      tabLogs.set(tabId, []);
    }
    
    const logs = tabLogs.get(tabId)!;
    logs.push({
      ...message,
      tabId: tabId,
      tabUrl: sender.tab.url
    });
    
    // Keep only the last 100 logs per tab to prevent memory issues
    if (logs.length > 100) {
      logs.splice(0, logs.length - 100);
    }
    
    // Forward the message to any listening popups
    chrome.runtime.sendMessage({
      type: 'CONSOLE_LOG_FORWARDED',
      ...message,
      tabId: tabId,
      tabUrl: sender.tab.url
    }).catch(() => {
      // Ignore errors if no popup is listening
    });
  }
  
  // <PERSON>le requests for stored logs
  if (message.type === 'GET_TAB_LOGS') {
    const tabId = message.tabId;
    const logs = tabLogs.get(tabId) || [];
    sendResponse({ logs });
  }
});

// Clean up logs when tabs are closed
chrome.tabs.onRemoved.addListener((tabId) => {
  tabLogs.delete(tabId);
});

// Clean up logs when tabs are updated (navigated to new URL)
chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
  if (changeInfo.url) {
    // Clear logs when navigating to a new URL
    tabLogs.delete(tabId);
  }
});

// Content script that runs in the context of web pages
// This bridges the gap between injected scripts and the extension

console.log('Trad Console Monitor content script loaded');

// Listen for messages from the injected script
window.addEventListener('message', (event) => {
  // Only accept messages from the same origin
  if (event.source !== window) return;
  
  if (event.data.type === 'CONSOLE_LOG_FROM_PAGE') {
    // Forward the message to the extension
    chrome.runtime.sendMessage({
      type: 'CONSOLE_LOG',
      level: event.data.level,
      args: event.data.args,
      timestamp: event.data.timestamp,
      url: window.location.href
    });
  }
});

// Inject the console monitoring script into the page
const script = document.createElement('script');
script.textContent = `
  (function() {
    // Only inject if not already injected
    if (window.__consoleMonitorInjected) return;
    window.__consoleMonitorInjected = true;
    
    console.log('Console monitor injected into page');
    
    // Store original console methods
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;
    const originalInfo = console.info;
    
    // Override console methods to capture logs
    console.log = (...args) => {
      originalLog.apply(console, args);
      window.postMessage({
        type: 'CONSOLE_LOG_FROM_PAGE',
        level: 'log',
        args: args.map(arg => {
          try {
            return typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg);
          } catch (e) {
            return String(arg);
          }
        }),
        timestamp: Date.now()
      }, '*');
    };
    
    console.warn = (...args) => {
      originalWarn.apply(console, args);
      window.postMessage({
        type: 'CONSOLE_LOG_FROM_PAGE',
        level: 'warn',
        args: args.map(arg => {
          try {
            return typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg);
          } catch (e) {
            return String(arg);
          }
        }),
        timestamp: Date.now()
      }, '*');
    };
    
    console.error = (...args) => {
      originalError.apply(console, args);
      window.postMessage({
        type: 'CONSOLE_LOG_FROM_PAGE',
        level: 'error',
        args: args.map(arg => {
          try {
            return typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg);
          } catch (e) {
            return String(arg);
          }
        }),
        timestamp: Date.now()
      }, '*');
    };
    
    console.info = (...args) => {
      originalInfo.apply(console, args);
      window.postMessage({
        type: 'CONSOLE_LOG_FROM_PAGE',
        level: 'info',
        args: args.map(arg => {
          try {
            return typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg);
          } catch (e) {
            return String(arg);
          }
        }),
        timestamp: Date.now()
      }, '*');
    };
  })();
`;

// Insert the script into the page
(document.head || document.documentElement).appendChild(script);
script.remove();

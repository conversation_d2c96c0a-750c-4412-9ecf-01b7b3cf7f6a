import { useEffect, useState } from "react"

function IndexPopup() {
  const [data, setData] = useState("")
  const [title, setTitle] = useState<string>("Loading...")
  const [currentTabId, setCurrentTabId] = useState<number | null>(null)
  useEffect(() => {
    // Query the active tab in the current window
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {

      const tab = tabs?.[0]
      // Some system pages (chrome://, about:) won’t expose titles
      setTitle(tab?.title || "(no title or unavailable)")
    })
  }, [])

  useEffect(() => {
    // Listen for console messages forwarded from background script
    const messageListener = (message: any) => {
      if (message.type === 'CONSOLE_LOG_FORWARDED' && message.tabId === currentTabId) {
        const timestamp = new Date(message.timestamp).toLocaleTimeString();
        const logEntry = `[${timestamp}] [${message.level.toUpperCase()}] ${message.args.join(' ')}`;
        setData(prev => prev ? `${prev}\n${logEntry}` : logEntry);
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    // Function to load existing logs for a tab
    const loadTabLogs = (tabId: number) => {
      chrome.runtime.sendMessage({ type: 'GET_TAB_LOGS', tabId }, (response) => {
        if (response && response.logs) {
          const logEntries = response.logs.map((log: any) => {
            const timestamp = new Date(log.timestamp).toLocaleTimeString();
            return `[${timestamp}] [${log.level.toUpperCase()}] ${log.args.join(' ')}`;
          });
          setData(logEntries.join('\n'));
        } else {
          setData('');
        }
      });
    };

    // Monitor active tab changes
    const handleTabUpdate = () => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs?.[0];
        if (tab?.id && tab.id !== currentTabId) {
          setCurrentTabId(tab.id);
          // Load existing logs for this tab
          loadTabLogs(tab.id);
        }
      });
    };

    // Initial setup
    handleTabUpdate();

    // Listen for tab activation changes
    chrome.tabs.onActivated.addListener(handleTabUpdate);
    chrome.tabs.onUpdated.addListener((_, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.active) {
        handleTabUpdate();
      }
    });

    // Cleanup
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
      chrome.tabs.onActivated.removeListener(handleTabUpdate);
      chrome.tabs.onUpdated.removeListener(handleTabUpdate);
    };
  }, [currentTabId])

  const clearLogs = () => {
    setData('');
  };

  return (
    <div
      style={{
        padding: 16,
        width: 400,
        height: 500
      }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 10 }}>
        <h2 style={{ margin: 0 }}>
          {title}
        </h2>
        <button
          onClick={clearLogs}
          style={{
            padding: '4px 8px',
            fontSize: 12,
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: 4,
            cursor: 'pointer'
          }}>
          Clear
        </button>
      </div>
      <h3 style={{ margin: '10px 0' }}>Console Logs:</h3>
      <div
        style={{
          backgroundColor: '#1e1e1e',
          color: '#ffffff',
          padding: 10,
          borderRadius: 4,
          fontFamily: 'monospace',
          fontSize: 12,
          height: 350,
          overflowY: 'auto',
          whiteSpace: 'pre-wrap',
          border: '1px solid #333'
        }}>
        {data || 'Waiting for console logs...\n\nMake sure to:\n1. Navigate to a webpage\n2. Open browser console and run: console.log("test")\n3. Check this popup for captured logs'}
      </div>
      <div style={{ marginTop: 10, fontSize: 12, color: '#666' }}>
        Console logs from the active tab will appear above
      </div>
    </div>
  )
}

export default IndexPopup

import { useEffect, useState } from "react"

function IndexPopup() {
  const [data, setData] = useState("")
  const [title, setTitle] = useState<string>("Loading...")
  useEffect(() => {
    // Query the active tab in the current window
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {

      const tab = tabs?.[0]
      // Some system pages (chrome://, about:) won’t expose titles
      setTitle(tab?.title || "(no title or unavailable)")
    })
  }, [])

  useEffect(() => {
    // read console log
    chrome.extension.getBackgroundPage().console.log("hello")
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      const tab = tabs?.[0]
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: () => {
          return console.log
        }
      }, (injectionResults) => {
        console.log(injectionResults)
        setData(injectionResults[0].result.toString())
      })
    })

  },[])

  return (
    <div
      style={{
        padding: 16
      }}>
      <h2>
        {title}
      </h2>
      <h3> Console: {data}</h3>
    </div>
  )
}

export default IndexPopup

import { useEffect, useState } from "react"

function IndexPopup() {
  const [data, setData] = useState("")
  const [title, setTitle] = useState<string>("Loading...")
  useEffect(() => {
    // Query the active tab in the current window
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {

      const tab = tabs?.[0]
      // Some system pages (chrome://, about:) won’t expose titles
      setTitle(tab?.title || "(no title or unavailable)")
    })
  }, [])

  useEffect(() => {
    let currentTabId: number | null = null;

    // Function to inject console monitoring script
    const injectConsoleMonitor = (tabId: number) => {
      chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          // Only inject if not already injected
          if ((window as any).__consoleMonitorInjected) return;
          (window as any).__consoleMonitorInjected = true;

          // Store original console methods
          const originalLog = console.log;
          const originalWarn = console.warn;
          const originalError = console.error;
          const originalInfo = console.info;

          // Override console methods to capture logs
          console.log = (...args: any[]) => {
            originalLog.apply(console, args);
            chrome.runtime.sendMessage({
              type: 'CONSOLE_LOG',
              level: 'log',
              args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
              timestamp: Date.now()
            });
          };

          console.warn = (...args: any[]) => {
            originalWarn.apply(console, args);
            chrome.runtime.sendMessage({
              type: 'CONSOLE_LOG',
              level: 'warn',
              args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
              timestamp: Date.now()
            });
          };

          console.error = (...args: any[]) => {
            originalError.apply(console, args);
            chrome.runtime.sendMessage({
              type: 'CONSOLE_LOG',
              level: 'error',
              args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
              timestamp: Date.now()
            });
          };

          console.info = (...args: any[]) => {
            originalInfo.apply(console, args);
            chrome.runtime.sendMessage({
              type: 'CONSOLE_LOG',
              level: 'info',
              args: args.map(arg => typeof arg === 'object' ? JSON.stringify(arg) : String(arg)),
              timestamp: Date.now()
            });
          };
        }
      });
    };

    // Listen for console messages from content scripts
    const messageListener = (message: any, sender: chrome.runtime.MessageSender) => {
      if (message.type === 'CONSOLE_LOG' && sender.tab?.id === currentTabId) {
        const logEntry = `[${message.level.toUpperCase()}] ${message.args.join(' ')}`;
        setData(prev => prev ? `${prev}\n${logEntry}` : logEntry);
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);

    // Monitor active tab changes
    const handleTabUpdate = () => {
      chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        const tab = tabs?.[0];
        if (tab?.id && tab.id !== currentTabId) {
          currentTabId = tab.id;
          // Clear previous logs when switching tabs
          setData('');
          // Inject console monitor into new active tab
          injectConsoleMonitor(tab.id);
        }
      });
    };

    // Initial setup
    handleTabUpdate();

    // Listen for tab activation changes
    chrome.tabs.onActivated.addListener(handleTabUpdate);
    chrome.tabs.onUpdated.addListener((_, changeInfo, tab) => {
      if (changeInfo.status === 'complete' && tab.active) {
        handleTabUpdate();
      }
    });

    // Cleanup
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
      chrome.tabs.onActivated.removeListener(handleTabUpdate);
      chrome.tabs.onUpdated.removeListener(handleTabUpdate);
    };
  }, [])

  return (
    <div
      style={{
        padding: 16,
        width: 400,
        height: 500
      }}>
      <h2>
        {title}
      </h2>
      <h3>Console Logs:</h3>
      <div
        style={{
          backgroundColor: '#1e1e1e',
          color: '#ffffff',
          padding: 10,
          borderRadius: 4,
          fontFamily: 'monospace',
          fontSize: 12,
          height: 350,
          overflowY: 'auto',
          whiteSpace: 'pre-wrap',
          border: '1px solid #333'
        }}>
        {data || 'Waiting for console logs...'}
      </div>
      <div style={{ marginTop: 10, fontSize: 12, color: '#666' }}>
        Switch to a tab and check its console output
      </div>
    </div>
  )
}

export default IndexPopup
